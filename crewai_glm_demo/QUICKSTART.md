# CrewAI + GLM-4 快速入门指南

## 🎯 5分钟快速体验

### 步骤1：获取API密钥

1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册账号并获取API密钥
3. 确保账户有足够的余额

### 步骤2：配置环境

```bash
# 编辑.env文件
nano .env

# 设置你的API密钥
ZHIPUAI_API_KEY=你的实际API密钥
```

### 步骤3：运行测试

```bash
# 测试环境配置
python test_demo.py
```

如果看到"🎉 所有测试通过！"，说明环境配置正确。

### 步骤4：开始研究

```bash
# 运行演示程序
python main.py
```

按照提示输入研究主题，例如："人工智能大模型"

### 步骤5：查看结果

程序完成后，查看生成的报告：

```bash
cat output/research_report.md
```

## 🔧 常用命令

```bash
# 直接指定主题
python main.py --topic "量子计算"

# 静默模式（适合脚本调用）
python main.py --topic "区块链" --quiet

# 运行完整测试
python test_demo.py
```

## 📚 学习路径

### 初学者路径

1. **理解基本概念**
   - 阅读 `README.md` 中的核心概念部分
   - 了解代理、任务、团队的关系

2. **查看代码结构**
   - 从 `main.py` 开始理解程序流程
   - 查看 `agents.py` 了解代理定义
   - 查看 `tasks.py` 了解任务设计

3. **实践修改**
   - 修改代理的角色和背景故事
   - 调整任务的描述和期望输出
   - 尝试不同的研究主题

### 进阶学习

1. **自定义代理**
   - 添加新的代理角色
   - 为代理配置不同的工具
   - 调整模型参数

2. **扩展任务流程**
   - 添加更多任务步骤
   - 设计复杂的任务依赖关系
   - 实现并行任务执行

3. **集成外部工具**
   - 添加搜索工具
   - 集成数据库查询
   - 连接外部API

## 🎨 自定义示例

### 修改代理角色

编辑 `config/agents.yaml`：

```yaml
data_scientist:
  role: 数据科学家
  goal: 分析数据并发现模式
  backstory: 你是一位经验丰富的数据科学家...

market_analyst:
  role: 市场分析师  
  goal: 分析市场趋势和商业机会
  backstory: 你专注于市场研究和商业分析...
```

### 添加新任务

编辑 `config/tasks.yaml`：

```yaml
data_analysis_task:
  description: 对收集的数据进行深入分析...
  expected_output: 数据分析报告，包含图表和洞察...
  agent: data_scientist

market_research_task:
  description: 研究市场趋势和竞争格局...
  expected_output: 市场分析报告...
  agent: market_analyst
```

### 调整模型参数

编辑 `llm_config.py`：

```python
# 为创意任务配置
CREATIVE_LLM_CONFIG = {
    "temperature": 0.9,  # 高创造性
    "max_tokens": 3000
}

# 为分析任务配置
ANALYTICAL_LLM_CONFIG = {
    "temperature": 0.1,  # 高准确性
    "max_tokens": 2000
}
```

## 🚨 故障排除

### 常见错误及解决方案

1. **API密钥错误**
   ```
   错误：请在.env文件中设置ZHIPUAI_API_KEY
   解决：检查.env文件中的API密钥是否正确
   ```

2. **网络连接失败**
   ```
   错误：GLM-4连接失败
   解决：检查网络连接，确保能访问智谱AI服务
   ```

3. **依赖包缺失**
   ```
   错误：ModuleNotFoundError
   解决：重新安装依赖包
   pip install crewai crewai-tools langchain-openai python-dotenv
   ```

4. **内存不足**
   ```
   错误：CUDA out of memory
   解决：减少max_tokens参数或使用更小的模型
   ```

### 调试技巧

1. **启用详细日志**
   ```python
   crew = Crew(..., verbose=True)
   ```

2. **单独测试组件**
   ```bash
   python test_demo.py
   ```

3. **检查输出文件**
   ```bash
   ls -la output/
   cat output/research_report.md
   ```

## 💡 使用技巧

### 提高输出质量

1. **优化提示词**
   - 使用具体、清晰的描述
   - 提供示例格式
   - 明确期望的输出结构

2. **调整模型参数**
   - 准确性任务：降低temperature
   - 创意性任务：提高temperature
   - 长文本：增加max_tokens

3. **合理设计任务流程**
   - 将复杂任务分解为简单步骤
   - 设置合理的任务依赖关系
   - 为每个代理分配合适的工具

### 性能优化

1. **并行处理**
   - 对于独立的任务，考虑并行执行
   - 使用异步调用减少等待时间

2. **缓存机制**
   - 启用代理记忆功能
   - 缓存常用的查询结果

3. **资源管理**
   - 合理设置超时时间
   - 监控API调用频率和成本

## 🎓 学习资源

### 官方文档
- [CrewAI文档](https://docs.crewai.com/)
- [智谱AI API文档](https://open.bigmodel.cn/dev/api)

### 社区资源
- [CrewAI GitHub](https://github.com/crewAIInc/crewAI)
- [示例项目集合](https://github.com/crewAIInc/crewAI-examples)

### 相关技术
- [LangChain](https://python.langchain.com/)
- [OpenAI API](https://platform.openai.com/docs)

## 🚀 下一步

完成快速入门后，你可以：

1. **探索更多工具** - 尝试集成搜索、数据库等工具
2. **构建复杂流程** - 设计多步骤的研究和分析流程
3. **开发实际应用** - 将CrewAI应用到实际的业务场景
4. **参与社区** - 分享你的经验和改进建议

祝你学习愉快！🎉
