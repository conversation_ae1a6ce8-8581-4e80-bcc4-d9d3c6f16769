# CrewAI + GLM-4 AI研究团队演示

这是一个使用GLM-4大模型的CrewAI入门示例，帮助你学习如何构建AI代理团队来完成复杂的研究任务。

## 🎯 项目概述

本项目演示了如何使用CrewAI框架和GLM-4大模型创建一个AI研究团队，该团队包含两个专业代理：
- **研究员** - 负责收集和分析信息
- **分析师** - 负责生成专业报告

## 🏗️ 项目结构

```
crewai_glm_demo/
├── README.md              # 项目说明文档
├── .env                   # 环境变量配置
├── main.py               # 主程序入口
├── test_demo.py          # 测试脚本
├── llm_config.py         # GLM-4模型配置
├── agents.py             # 代理定义
├── tasks.py              # 任务定义
├── crew.py               # 团队协调逻辑
├── config/               # 配置文件目录
│   ├── agents.yaml       # 代理配置
│   └── tasks.yaml        # 任务配置
└── output/               # 输出文件目录
    └── research_report.md # 生成的研究报告
```

## 🚀 快速开始

### 1. 环境准备

确保你已经安装了Python 3.11和相关依赖：

```bash
# 创建虚拟环境
pyenv virtualenv 3.11.11 crewai_glm_demo_3.11.11
pyenv local crewai_glm_demo_3.11.11

# 安装依赖
pip install crewai crewai-tools langchain-openai python-dotenv
```

### 2. 配置API密钥

编辑 `.env` 文件，设置你的智谱AI API密钥：

```bash
# 获取API密钥: https://open.bigmodel.cn/
ZHIPUAI_API_KEY=your_actual_api_key_here
```

### 3. 运行测试

首先运行测试脚本确保环境配置正确：

```bash
cd crewai_glm_demo
python test_demo.py
```

### 4. 运行演示

运行主程序开始AI研究：

```bash
python main.py
```

或者直接指定研究主题：

```bash
python main.py --topic "人工智能大模型"
```

## 📚 CrewAI核心概念学习

### 1. 代理 (Agents)

代理是CrewAI中的核心概念，每个代理都有特定的角色、目标和背景故事。

```python
# 创建研究员代理
researcher = Agent(
    role="高级数据研究员",
    goal="发现和分析指定主题的最新发展动态",
    backstory="你是一位经验丰富的研究员...",
    tools=[search_tool],  # 可用工具
    llm=glm_llm,         # 使用的语言模型
    verbose=True,        # 显示详细日志
    memory=True          # 启用记忆功能
)
```

**关键属性说明：**
- `role`: 代理的角色定义
- `goal`: 代理的目标
- `backstory`: 代理的背景故事，影响其行为
- `tools`: 代理可以使用的工具列表
- `llm`: 代理使用的语言模型
- `verbose`: 是否显示详细执行过程
- `memory`: 是否启用记忆功能

### 2. 任务 (Tasks)

任务定义了代理需要完成的具体工作。

```python
# 创建研究任务
research_task = Task(
    description="对指定主题进行全面研究...",
    expected_output="包含10个要点的详细列表...",
    agent=researcher,           # 执行任务的代理
    context=[previous_task],    # 依赖的上下文任务
    output_file="report.md"     # 输出文件（可选）
)
```

**关键属性说明：**
- `description`: 任务的详细描述
- `expected_output`: 期望的输出格式和内容
- `agent`: 负责执行任务的代理
- `context`: 依赖的其他任务（用于任务链）
- `output_file`: 输出文件路径（可选）

### 3. 团队 (Crew)

团队协调多个代理和任务的执行。

```python
# 创建团队
crew = Crew(
    agents=[researcher, analyst],      # 团队成员
    tasks=[research_task, report_task], # 任务列表
    process=Process.sequential,        # 执行流程
    verbose=True,                      # 详细日志
    memory=True                        # 团队记忆
)

# 启动团队工作
result = crew.kickoff()
```

**执行流程类型：**
- `Process.sequential`: 顺序执行任务
- `Process.hierarchical`: 分层执行（需要管理者代理）

## 🔧 GLM-4模型集成

### 模型配置

本项目使用智谱AI的GLM-4模型，通过OpenAI兼容接口调用：

```python
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(
    model="glm-4",
    openai_api_key="your_zhipuai_api_key",
    openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
    temperature=0.7,    # 控制输出随机性
    max_tokens=2000,    # 最大输出长度
    timeout=60          # 请求超时时间
)
```

### 不同代理的模型配置

可以为不同代理配置不同的模型参数：

```python
# 研究员：需要更准确的输出
RESEARCHER_LLM_CONFIG = {
    "temperature": 0.3,  # 较低的随机性
    "max_tokens": 2500
}

# 分析师：需要平衡创造性和准确性
ANALYST_LLM_CONFIG = {
    "temperature": 0.5,  # 中等随机性
    "max_tokens": 3000
}
```

## 🛠️ 工具集成

CrewAI支持多种工具来增强代理能力：

### 搜索工具

```python
from crewai_tools import SerperDevTool, WebsiteSearchTool

# 使用Serper搜索（需要API密钥）
search_tool = SerperDevTool()

# 或使用网站搜索工具
website_tool = WebsiteSearchTool()
```

### 自定义工具

你也可以创建自定义工具：

```python
from crewai_tools import tool

@tool("计算器")
def calculator(expression: str) -> str:
    """计算数学表达式"""
    try:
        result = eval(expression)
        return f"计算结果: {result}"
    except:
        return "计算错误"
```

## 📊 任务流程设计

### 顺序执行流程

本演示使用顺序执行流程：

1. **研究任务** - 研究员收集信息
2. **报告任务** - 分析师基于研究结果生成报告

### 任务依赖

通过`context`参数建立任务依赖关系：

```python
reporting_task = Task(
    description="基于研究结果生成报告...",
    agent=analyst,
    context=[research_task]  # 依赖研究任务的结果
)
```

## 🎨 自定义和扩展

### 添加新代理

1. 在`agents.py`中定义新代理
2. 在`config/agents.yaml`中添加配置
3. 在团队中注册新代理

### 添加新任务

1. 在`tasks.py`中定义新任务
2. 在`config/tasks.yaml`中添加配置
3. 设置任务依赖关系

### 集成新工具

1. 安装工具包：`pip install crewai-tools`
2. 在代理中配置工具
3. 测试工具功能

## 🔍 调试和优化

### 启用详细日志

```python
crew = Crew(
    agents=[...],
    tasks=[...],
    verbose=True  # 显示详细执行过程
)
```

### 调整模型参数

根据任务需求调整temperature和max_tokens：
- 需要准确性：降低temperature (0.1-0.3)
- 需要创造性：提高temperature (0.7-0.9)
- 长文本生成：增加max_tokens

### 优化提示词

在代理的`backstory`和任务的`description`中使用清晰、具体的提示词。

## 📝 最佳实践

1. **明确角色分工** - 每个代理应有清晰的职责
2. **详细任务描述** - 提供具体的任务要求和期望输出
3. **合理工具配置** - 为代理配置合适的工具
4. **测试驱动开发** - 先测试基本功能再扩展
5. **渐进式复杂化** - 从简单任务开始，逐步增加复杂性

## 🚨 常见问题

### API密钥问题
- 确保在`.env`文件中正确设置`ZHIPUAI_API_KEY`
- 检查API密钥是否有效且有足够余额

### 网络连接问题
- 确保能够访问智谱AI的API服务
- 如有代理，请配置相应的网络设置

### 内存不足
- 减少`max_tokens`参数
- 简化任务描述
- 分批处理大量数据

## 📖 进一步学习

- [CrewAI官方文档](https://docs.crewai.com/)
- [智谱AI API文档](https://open.bigmodel.cn/dev/api)
- [LangChain文档](https://python.langchain.com/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个演示项目！

## 📋 使用示例

### 基本使用

```bash
# 交互式运行
python main.py

# 指定主题运行
python main.py --topic "区块链技术"

# 静默模式运行
python main.py --topic "量子计算" --quiet
```

### 编程接口使用

```python
from crew import run_research, create_research_crew

# 方式1：直接运行研究
result = run_research("人工智能大模型")

# 方式2：创建团队后运行
crew = create_research_crew("新能源汽车")
result = crew.kickoff()

# 获取团队信息
info = crew.get_crew_info()
print(info)
```

### 自定义配置

```python
from llm_config import get_glm_llm_for_agent
from agents import create_researcher_agent

# 自定义模型参数
custom_llm = get_glm_llm_for_agent(temperature=0.2, max_tokens=1500)

# 创建自定义代理
researcher = create_researcher_agent()
researcher.llm = custom_llm
```
