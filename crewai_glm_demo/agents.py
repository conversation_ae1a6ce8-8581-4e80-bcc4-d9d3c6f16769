"""
CrewAI智能代理定义模块
定义研究员和报告分析师两个代理
"""

from crewai import Agent
from crewai_tools import SerperDevTool, WebsiteSearchTool
from llm_config import get_glm_llm_for_agent, RESEARCHER_LLM_CONFIG, ANALYST_LLM_CONFIG

def create_researcher_agent():
    """
    创建研究员代理
    
    Returns:
        Agent: 配置好的研究员代理
    """
    # 获取研究员专用的GLM模型
    llm = get_glm_llm_for_agent(**RESEARCHER_LLM_CONFIG)
    
    # 配置研究工具（如果有Serper API密钥的话）
    tools = []
    try:
        # 尝试使用Serper搜索工具（需要SERPER_API_KEY环境变量）
        search_tool = SerperDevTool()
        tools.append(search_tool)
    except:
        # 如果没有Serper API，使用网站搜索工具
        website_tool = WebsiteSearchTool()
        tools.append(website_tool)
    
    researcher = Agent(
        role="高级数据研究员",
        goal="发现和分析指定主题的最新发展动态和趋势",
        backstory="""
        你是一位经验丰富的研究员，拥有超过10年的行业研究经验。
        你擅长从海量信息中提取最有价值的内容，并能够识别重要的发展趋势。
        你的研究方法严谨，总是能够找到最权威和最新的信息源。
        你特别擅长技术领域的研究，能够理解复杂的技术概念并将其转化为易懂的信息。
        """,
        tools=tools,
        llm=llm,
        verbose=True,
        allow_delegation=False,  # 不允许委托给其他代理
        max_iter=3,  # 最大迭代次数
        memory=True  # 启用记忆功能
    )
    
    return researcher

def create_reporting_analyst_agent():
    """
    创建报告分析师代理
    
    Returns:
        Agent: 配置好的报告分析师代理
    """
    # 获取分析师专用的GLM模型
    llm = get_glm_llm_for_agent(**ANALYST_LLM_CONFIG)
    
    analyst = Agent(
        role="专业报告分析师",
        goal="将研究数据转化为结构化、专业的分析报告",
        backstory="""
        你是一位资深的报告分析师，拥有丰富的数据分析和报告撰写经验。
        你擅长将复杂的研究数据整理成逻辑清晰、结构完整的专业报告。
        你的报告总是能够突出重点，提供有价值的洞察和建议。
        你特别注重报告的可读性和实用性，确保读者能够快速理解核心内容。
        你的写作风格专业而不失生动，能够让技术内容变得易于理解。
        """,
        tools=[],  # 分析师主要负责分析和写作，不需要搜索工具
        llm=llm,
        verbose=True,
        allow_delegation=False,
        max_iter=2,
        memory=True
    )
    
    return analyst

def get_all_agents():
    """
    获取所有代理的字典
    
    Returns:
        dict: 包含所有代理的字典
    """
    return {
        "researcher": create_researcher_agent(),
        "reporting_analyst": create_reporting_analyst_agent()
    }
