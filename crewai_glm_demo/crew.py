"""
CrewAI团队协调模块
实现AI研究团队的协调逻辑
"""

import os
from crewai import Crew, Process
from agents import create_researcher_agent, create_reporting_analyst_agent
from tasks import create_research_task, create_reporting_task
from llm_config import get_glm_llm

class AIResearchCrew:
    """
    AI研究团队类
    负责协调研究员和分析师的工作
    """
    
    def __init__(self, topic: str, verbose: bool = True):
        """
        初始化AI研究团队
        
        Args:
            topic (str): 研究主题
            verbose (bool): 是否显示详细日志
        """
        self.topic = topic
        self.verbose = verbose
        
        # 创建代理
        self.researcher = create_researcher_agent()
        self.analyst = create_reporting_analyst_agent()
        
        # 创建任务
        self.research_task = create_research_task(topic, self.researcher)
        self.reporting_task = create_reporting_task(topic, self.analyst, self.research_task)
        
        # 创建团队
        self.crew = self._create_crew()
    
    def _create_crew(self):
        """
        创建CrewAI团队
        
        Returns:
            Crew: 配置好的CrewAI团队
        """
        crew = Crew(
            agents=[self.researcher, self.analyst],
            tasks=[self.research_task, self.reporting_task],
            process=Process.sequential,  # 顺序执行任务
            verbose=self.verbose,
            memory=True,  # 启用团队记忆
            embedder={
                "provider": "openai",
                "config": {
                    "api_key": os.getenv("ZHIPUAI_API_KEY"),
                    "model": "text-embedding-ada-002"
                }
            } if os.getenv("ZHIPUAI_API_KEY") else None
        )
        
        return crew
    
    def kickoff(self):
        """
        启动团队工作
        
        Returns:
            str: 团队工作结果
        """
        print(f"🚀 启动AI研究团队，开始研究主题: {self.topic}")
        print("=" * 60)
        
        try:
            # 执行团队任务
            result = self.crew.kickoff()
            
            print("=" * 60)
            print("✅ 团队工作完成！")
            print(f"📄 报告已保存到: output/research_report.md")
            
            return result
            
        except Exception as e:
            print(f"❌ 团队工作出现错误: {str(e)}")
            raise e
    
    def get_crew_info(self):
        """
        获取团队信息
        
        Returns:
            dict: 团队信息
        """
        return {
            "topic": self.topic,
            "agents": [
                {
                    "role": self.researcher.role,
                    "goal": self.researcher.goal
                },
                {
                    "role": self.analyst.role,
                    "goal": self.analyst.goal
                }
            ],
            "tasks": [
                {
                    "description": self.research_task.description[:100] + "...",
                    "agent": self.research_task.agent.role
                },
                {
                    "description": self.reporting_task.description[:100] + "...",
                    "agent": self.reporting_task.agent.role
                }
            ]
        }

def create_research_crew(topic: str, verbose: bool = True):
    """
    创建AI研究团队的便捷函数
    
    Args:
        topic (str): 研究主题
        verbose (bool): 是否显示详细日志
        
    Returns:
        AIResearchCrew: 配置好的AI研究团队
    """
    return AIResearchCrew(topic, verbose)

def run_research(topic: str, verbose: bool = True):
    """
    运行完整的研究流程
    
    Args:
        topic (str): 研究主题
        verbose (bool): 是否显示详细日志
        
    Returns:
        str: 研究结果
    """
    crew = create_research_crew(topic, verbose)
    return crew.kickoff()
