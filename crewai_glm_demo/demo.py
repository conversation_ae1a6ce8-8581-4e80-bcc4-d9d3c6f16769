#!/usr/bin/env python3
"""
CrewAI + GLM-4 演示脚本
展示如何使用GLM大模型进行AI团队协作
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def demo_without_api():
    """
    无需API密钥的演示 - 展示系统架构和配置
    """
    print("🎯 CrewAI + GLM-4 系统架构演示")
    print("=" * 60)
    
    # 展示代理配置
    print("\n👥 AI团队成员:")
    print("1. 🔬 研究员 (Researcher)")
    print("   - 角色: 高级数据研究员")
    print("   - 目标: 发现和分析指定主题的最新发展动态")
    print("   - 工具: 搜索工具、网站分析工具")
    print("   - 模型配置: temperature=0.3 (高准确性)")
    
    print("\n2. 📊 分析师 (Analyst)")
    print("   - 角色: 专业报告分析师")
    print("   - 目标: 将研究数据转化为结构化报告")
    print("   - 工具: 无需外部工具，专注分析和写作")
    print("   - 模型配置: temperature=0.5 (平衡创造性和准确性)")
    
    # 展示任务流程
    print("\n📋 任务执行流程:")
    print("1. 研究任务 → 研究员执行")
    print("   ├─ 收集主题相关信息")
    print("   ├─ 分析技术趋势")
    print("   ├─ 识别关键发展")
    print("   └─ 输出: 10个关键要点列表")
    
    print("\n2. 报告任务 → 分析师执行")
    print("   ├─ 基于研究结果")
    print("   ├─ 生成结构化报告")
    print("   ├─ 提供专业洞察")
    print("   └─ 输出: 完整的markdown报告")
    
    # 展示GLM-4配置
    print("\n🤖 GLM-4模型配置:")
    print("- 模型: glm-4")
    print("- API基础URL: https://open.bigmodel.cn/api/paas/v4/")
    print("- 兼容接口: OpenAI API")
    print("- 支持功能: 文本生成、对话、分析")
    
    # 展示项目结构
    print("\n📁 项目结构:")
    print("crewai_glm_demo/")
    print("├── main.py           # 主程序入口")
    print("├── llm_config.py     # GLM-4模型配置")
    print("├── agents.py         # 代理定义")
    print("├── tasks.py          # 任务定义")
    print("├── crew.py           # 团队协调逻辑")
    print("├── config/           # 配置文件")
    print("│   ├── agents.yaml   # 代理配置")
    print("│   └── tasks.yaml    # 任务配置")
    print("└── output/           # 输出目录")
    
    print("\n" + "=" * 60)

def demo_code_examples():
    """
    展示代码使用示例
    """
    print("\n💻 代码使用示例:")
    print("=" * 60)
    
    print("\n1. 基本使用:")
    print("```python")
    print("from crew import run_research")
    print("")
    print("# 直接运行研究")
    print('result = run_research("人工智能大模型")')
    print("```")
    
    print("\n2. 自定义团队:")
    print("```python")
    print("from crew import create_research_crew")
    print("")
    print("# 创建自定义团队")
    print('crew = create_research_crew("量子计算")')
    print("result = crew.kickoff()")
    print("```")
    
    print("\n3. 单独使用代理:")
    print("```python")
    print("from agents import create_researcher_agent")
    print("from tasks import create_research_task")
    print("")
    print("# 创建研究员和任务")
    print("researcher = create_researcher_agent()")
    print('task = create_research_task("区块链技术")')
    print("```")
    
    print("\n4. 自定义GLM配置:")
    print("```python")
    print("from llm_config import get_glm_llm_for_agent")
    print("")
    print("# 自定义模型参数")
    print("llm = get_glm_llm_for_agent(")
    print("    temperature=0.2,  # 更准确")
    print("    max_tokens=3000   # 更长输出")
    print(")")
    print("```")

def demo_learning_path():
    """
    展示学习路径
    """
    print("\n📚 CrewAI学习路径:")
    print("=" * 60)
    
    print("\n🎯 初学者 (第1-2天):")
    print("1. 理解核心概念:")
    print("   - Agent (代理): 具有特定角色的AI助手")
    print("   - Task (任务): 代理需要完成的具体工作")
    print("   - Crew (团队): 协调多个代理协作")
    print("   - Tool (工具): 代理可以使用的外部功能")
    
    print("\n2. 运行基础示例:")
    print("   - 配置API密钥")
    print("   - 运行test_demo.py测试")
    print("   - 尝试不同研究主题")
    
    print("\n🚀 进阶者 (第3-5天):")
    print("1. 自定义代理:")
    print("   - 修改代理角色和背景")
    print("   - 调整模型参数")
    print("   - 添加专用工具")
    
    print("\n2. 设计任务流程:")
    print("   - 创建多步骤任务")
    print("   - 设置任务依赖关系")
    print("   - 优化输出格式")
    
    print("\n🎓 专家级 (第6-10天):")
    print("1. 复杂系统设计:")
    print("   - 多代理协作")
    print("   - 并行任务执行")
    print("   - 错误处理和重试")
    
    print("\n2. 生产环境部署:")
    print("   - 性能优化")
    print("   - 监控和日志")
    print("   - 成本控制")

def demo_best_practices():
    """
    展示最佳实践
    """
    print("\n✨ CrewAI最佳实践:")
    print("=" * 60)
    
    print("\n1. 代理设计原则:")
    print("   ✅ 明确单一职责")
    print("   ✅ 详细背景故事")
    print("   ✅ 合适的工具配置")
    print("   ❌ 避免角色重叠")
    print("   ❌ 避免过于复杂的角色")
    
    print("\n2. 任务设计原则:")
    print("   ✅ 具体明确的描述")
    print("   ✅ 清晰的期望输出")
    print("   ✅ 合理的任务分解")
    print("   ❌ 避免模糊的要求")
    print("   ❌ 避免过大的任务")
    
    print("\n3. 提示词优化:")
    print("   ✅ 使用具体的例子")
    print("   ✅ 明确输出格式")
    print("   ✅ 提供上下文信息")
    print("   ❌ 避免歧义表达")
    print("   ❌ 避免过长的描述")
    
    print("\n4. 性能优化:")
    print("   ✅ 合理设置token限制")
    print("   ✅ 启用缓存机制")
    print("   ✅ 监控API调用")
    print("   ❌ 避免重复计算")
    print("   ❌ 避免无限循环")

def main():
    """
    主演示函数
    """
    print("🎉 欢迎使用 CrewAI + GLM-4 演示系统！")
    print("这是一个完整的AI团队协作学习示例")
    
    # 检查API密钥配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key or api_key == "your_zhipuai_api_key_here":
        print("\n⚠️  注意: 未检测到有效的API密钥")
        print("请在.env文件中设置ZHIPUAI_API_KEY以运行完整功能")
        print("获取API密钥: https://open.bigmodel.cn/")
    else:
        print(f"\n✅ 检测到API密钥: {api_key[:8]}...")
    
    # 运行演示
    demo_without_api()
    demo_code_examples()
    demo_learning_path()
    demo_best_practices()
    
    print("\n🚀 开始你的CrewAI学习之旅:")
    print("1. 设置API密钥: 编辑 .env 文件")
    print("2. 运行测试: python test_demo.py")
    print("3. 开始研究: python main.py")
    print("4. 查看文档: README.md 和 QUICKSTART.md")
    
    print("\n📖 学习资源:")
    print("- CrewAI官方文档: https://docs.crewai.com/")
    print("- 智谱AI平台: https://open.bigmodel.cn/")
    print("- 本项目GitHub: (你的项目地址)")
    
    print("\n🎯 祝你学习愉快！")

if __name__ == "__main__":
    main()
