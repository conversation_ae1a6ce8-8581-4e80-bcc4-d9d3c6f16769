"""
GLM-4大模型配置模块
用于配置CrewAI使用GLM-4作为LLM后端
"""

import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()

def get_glm_llm():
    """
    获取配置好的GLM-4模型实例

    Returns:
        ChatOpenAI: 配置好的GLM-4模型实例
    """
    # 从环境变量获取API密钥
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        raise ValueError("请在.env文件中设置ZHIPUAI_API_KEY")

    # 创建GLM-4模型实例
    llm = ChatOpenAI(
        model="glm-4",  # 使用GLM-4模型
        openai_api_key=api_key,  # 智谱AI的API密钥
        openai_api_base="https://open.bigmodel.cn/api/paas/v4/",  # 智谱AI的API基础URL
        temperature=0.7,  # 控制输出的随机性，0.7是一个平衡值
        max_tokens=1500,  # 减少最大输出token数
        timeout=120,  # 增加请求超时时间（秒）
        max_retries=3,  # 添加重试机制
    )

    return llm

def get_glm_llm_for_agent(temperature=0.7, max_tokens=1500):
    """
    为特定代理获取定制化的GLM-4模型实例

    Args:
        temperature (float): 输出随机性控制，范围0-1
        max_tokens (int): 最大输出token数

    Returns:
        ChatOpenAI: 定制化的GLM-4模型实例
    """
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        raise ValueError("请在.env文件中设置ZHIPUAI_API_KEY")

    llm = ChatOpenAI(
        model="glm-4",
        openai_api_key=api_key,
        openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
        temperature=temperature,
        max_tokens=max_tokens,
        timeout=120,  # 增加超时时间
        max_retries=3,  # 添加重试机制
    )

    return llm

# 预定义的模型配置
RESEARCHER_LLM_CONFIG = {
    "temperature": 0.3,  # 研究员需要更准确的输出
    "max_tokens": 1200  # 减少token数量
}

ANALYST_LLM_CONFIG = {
    "temperature": 0.5,  # 分析师需要平衡创造性和准确性
    "max_tokens": 1800  # 减少token数量
}
