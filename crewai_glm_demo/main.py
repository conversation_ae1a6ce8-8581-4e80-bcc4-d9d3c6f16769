#!/usr/bin/env python3
"""
CrewAI GLM演示主程序
使用GLM-4大模型的CrewAI入门示例
"""

import os
import sys
import argparse
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crew import run_research, create_research_crew
from llm_config import get_glm_llm

def check_environment():
    """
    检查环境配置
    
    Returns:
        bool: 环境是否配置正确
    """
    print("🔍 检查环境配置...")
    
    # 检查API密钥
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key or api_key == "your_zhipuai_api_key_here":
        print("❌ 错误: 请在.env文件中设置正确的ZHIPUAI_API_KEY")
        print("   获取API密钥: https://open.bigmodel.cn/")
        return False
    
    print("✅ API密钥配置正确")
    
    # 测试GLM连接
    try:
        print("🔗 测试GLM-4连接...")
        llm = get_glm_llm()
        response = llm.invoke("你好，请简单介绍一下你自己。")
        print(f"✅ GLM-4连接成功: {response.content[:50]}...")
        return True
    except Exception as e:
        print(f"❌ GLM-4连接失败: {str(e)}")
        return False

def print_welcome():
    """
    打印欢迎信息
    """
    print("=" * 80)
    print("🤖 CrewAI + GLM-4 AI研究团队演示")
    print("=" * 80)
    print("这是一个使用GLM-4大模型的CrewAI入门示例")
    print("团队成员:")
    print("  👨‍🔬 研究员 - 负责收集和分析信息")
    print("  📊 分析师 - 负责生成专业报告")
    print("=" * 80)

def get_research_topic():
    """
    获取研究主题
    
    Returns:
        str: 用户输入的研究主题
    """
    print("\n📝 请输入您想要研究的主题:")
    print("建议主题示例:")
    print("  - 人工智能大模型")
    print("  - 量子计算")
    print("  - 区块链技术")
    print("  - 新能源汽车")
    print("  - 元宇宙")
    
    while True:
        topic = input("\n请输入研究主题: ").strip()
        if topic:
            return topic
        print("❌ 主题不能为空，请重新输入")

def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="CrewAI GLM演示程序")
    parser.add_argument("--topic", type=str, help="研究主题")
    parser.add_argument("--verbose", action="store_true", default=True, help="显示详细日志")
    parser.add_argument("--quiet", action="store_true", help="静默模式")
    args = parser.parse_args()
    
    # 设置详细程度
    verbose = args.verbose and not args.quiet
    
    # 打印欢迎信息
    if not args.quiet:
        print_welcome()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 获取研究主题
    if args.topic:
        topic = args.topic
        print(f"\n📋 研究主题: {topic}")
    else:
        topic = get_research_topic()
    
    # 确认开始
    if not args.quiet:
        print(f"\n🎯 即将开始研究主题: '{topic}'")
        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("👋 已取消，再见！")
            sys.exit(0)
    
    # 记录开始时间
    start_time = datetime.now()
    print(f"\n⏰ 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行研究
        result = run_research(topic, verbose)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n⏰ 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总耗时: {duration.total_seconds():.1f}秒")
        
        # 显示结果摘要
        print("\n" + "=" * 80)
        print("📋 研究完成摘要:")
        print(f"  🎯 研究主题: {topic}")
        print(f"  📄 报告文件: output/research_report.md")
        print(f"  ⏱️  总耗时: {duration.total_seconds():.1f}秒")
        print("=" * 80)
        
        # 询问是否查看报告
        if not args.quiet:
            view = input("\n是否查看生成的报告? (y/N): ").strip().lower()
            if view in ['y', 'yes', '是']:
                try:
                    with open("output/research_report.md", "r", encoding="utf-8") as f:
                        content = f.read()
                    print("\n" + "=" * 80)
                    print("📄 生成的报告内容:")
                    print("=" * 80)
                    print(content)
                except FileNotFoundError:
                    print("❌ 报告文件未找到")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断了程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
