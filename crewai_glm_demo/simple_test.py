#!/usr/bin/env python3
"""
简化的CrewAI测试脚本
用于调试和验证基本功能
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_basic_llm():
    """
    测试基本的LLM功能
    """
    print("🧪 测试基本LLM功能...")
    
    try:
        from llm_config import get_glm_llm
        llm = get_glm_llm()
        
        # 简单测试
        response = llm.invoke("请简单介绍一下bolt.new这个工具。")
        print(f"✅ LLM响应成功")
        print(f"📝 响应内容: {response.content[:200]}...")
        return True
        
    except Exception as e:
        print(f"❌ LLM测试失败: {str(e)}")
        return False

def test_single_agent():
    """
    测试单个代理
    """
    print("\n🧪 测试单个代理...")
    
    try:
        from agents import create_researcher_agent
        
        # 创建研究员
        researcher = create_researcher_agent()
        print(f"✅ 研究员代理创建成功: {researcher.role}")
        
        # 简单的任务测试
        from crewai import Task
        
        simple_task = Task(
            description="请简单介绍bolt.new这个工具的主要功能和特点。",
            expected_output="一个包含3-5个要点的简单列表，介绍bolt.new的主要功能。",
            agent=researcher
        )
        
        print("🚀 执行简单任务...")
        result = simple_task.execute_sync()
        
        print(f"✅ 任务执行成功")
        print(f"📝 任务结果: {result[:300]}...")
        return True
        
    except Exception as e:
        print(f"❌ 单代理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_crew():
    """
    测试简化的团队
    """
    print("\n🧪 测试简化团队...")
    
    try:
        from crewai import Crew, Process, Agent, Task
        from llm_config import get_glm_llm_for_agent, RESEARCHER_LLM_CONFIG
        
        # 创建简化的代理
        llm = get_glm_llm_for_agent(**RESEARCHER_LLM_CONFIG)
        
        simple_agent = Agent(
            role="研究助手",
            goal="提供关于指定主题的基本信息",
            backstory="你是一个知识渊博的助手，能够提供准确的信息。",
            llm=llm,
            verbose=True,
            allow_delegation=False,
            max_iter=1,
            memory=False
        )
        
        # 创建简单任务
        simple_task = Task(
            description="请简单介绍bolt.new工具，包括它的主要功能和用途。",
            expected_output="一个包含3个要点的列表，简单介绍bolt.new。",
            agent=simple_agent
        )
        
        # 创建简化团队
        simple_crew = Crew(
            agents=[simple_agent],
            tasks=[simple_task],
            process=Process.sequential,
            verbose=True,
            memory=False
        )
        
        print("🚀 启动简化团队...")
        result = simple_crew.kickoff()
        
        print(f"✅ 团队执行成功")
        print(f"📝 团队结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 简化团队测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("🧪 CrewAI 简化测试")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key or api_key == "your_zhipuai_api_key_here":
        print("❌ 错误: 请设置正确的ZHIPUAI_API_KEY")
        return
    
    print(f"✅ API密钥已配置: {api_key[:8]}...")
    
    tests = [
        ("基本LLM功能", test_basic_llm),
        ("单个代理", test_single_agent),
        ("简化团队", test_simple_crew),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                break  # 如果基础测试失败，停止后续测试
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            break
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统基本功能正常。")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
