"""
CrewAI任务定义模块
定义研究任务和报告任务
"""

from crewai import Task
from agents import create_researcher_agent, create_reporting_analyst_agent

def create_research_task(topic: str, researcher_agent=None):
    """
    创建研究任务
    
    Args:
        topic (str): 研究主题
        researcher_agent (Agent, optional): 研究员代理，如果不提供则创建新的
        
    Returns:
        Task: 配置好的研究任务
    """
    if researcher_agent is None:
        researcher_agent = create_researcher_agent()
    
    research_task = Task(
        description=f"""
        对"{topic}"进行全面深入的研究调查。
        
        你的任务包括：
        1. 收集关于{topic}的最新信息和发展动态
        2. 分析当前的技术趋势和应用案例
        3. 识别关键的发展机遇和挑战
        4. 收集相关的数据、统计信息和专家观点
        5. 关注2024-2025年的最新发展
        
        请确保信息的准确性和时效性，重点关注：
        - 技术创新和突破
        - 市场应用和商业模式
        - 行业影响和未来趋势
        - 相关政策和标准
        """,
        expected_output=f"""
        一个包含10个关键要点的详细列表，涵盖{topic}的最重要信息：
        
        每个要点应该包含：
        - 简洁明了的标题
        - 详细的描述（2-3句话）
        - 具体的数据、案例或引用（如果有的话）
        - 时间信息（发生时间或预期时间）
        
        格式示例：
        1. **要点标题**: 详细描述内容，包含具体数据和案例。时间信息。
        2. **要点标题**: 详细描述内容，包含具体数据和案例。时间信息。
        ...
        
        确保信息的多样性，涵盖技术、市场、应用等不同维度。
        """,
        agent=researcher_agent,
        output_file=None  # 研究任务不直接输出文件
    )
    
    return research_task

def create_reporting_task(topic: str, analyst_agent=None, research_task=None):
    """
    创建报告任务
    
    Args:
        topic (str): 报告主题
        analyst_agent (Agent, optional): 分析师代理，如果不提供则创建新的
        research_task (Task, optional): 依赖的研究任务
        
    Returns:
        Task: 配置好的报告任务
    """
    if analyst_agent is None:
        analyst_agent = create_reporting_analyst_agent()
    
    # 设置任务依赖
    context = [research_task] if research_task else []
    
    reporting_task = Task(
        description=f"""
        基于研究员提供的信息，创建一份关于"{topic}"的专业分析报告。
        
        你的任务包括：
        1. 分析和整理研究员提供的所有信息
        2. 将信息组织成逻辑清晰的报告结构
        3. 为每个主要发现提供深入的分析
        4. 识别关键趋势和模式
        5. 提供专业的洞察和建议
        
        报告应该：
        - 结构清晰，逻辑性强
        - 内容详实，分析深入
        - 语言专业，易于理解
        - 包含具体的数据和案例
        - 提供有价值的洞察和建议
        """,
        expected_output=f"""
        一份完整的专业分析报告，包含以下结构：
        
        # {topic}发展现状与趋势分析报告
        
        ## 执行摘要
        - 核心发现总结（3-5个要点）
        - 主要趋势概述
        - 关键建议
        
        ## 详细分析
        ### 1. 技术发展现状
        - 当前技术水平
        - 主要技术突破
        - 技术发展趋势
        
        ### 2. 市场应用情况
        - 应用领域分析
        - 市场规模和增长
        - 商业模式创新
        
        ### 3. 行业影响评估
        - 对相关行业的影响
        - 机遇与挑战分析
        - 竞争格局变化
        
        ### 4. 未来发展预测
        - 短期发展预期（1-2年）
        - 中长期发展趋势（3-5年）
        - 潜在风险和机遇
        
        ## 结论与建议
        - 主要结论总结
        - 战略建议
        - 行动建议
        
        报告格式为markdown，不使用代码块标记。
        确保每个章节内容详实，分析深入。
        """,
        agent=analyst_agent,
        context=context,
        output_file="output/research_report.md"
    )
    
    return reporting_task

def create_all_tasks(topic: str):
    """
    创建完整的任务流程
    
    Args:
        topic (str): 研究和报告的主题
        
    Returns:
        tuple: (research_task, reporting_task)
    """
    # 创建代理
    researcher = create_researcher_agent()
    analyst = create_reporting_analyst_agent()
    
    # 创建任务
    research_task = create_research_task(topic, researcher)
    reporting_task = create_reporting_task(topic, analyst, research_task)
    
    return research_task, reporting_task
