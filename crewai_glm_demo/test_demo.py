#!/usr/bin/env python3
"""
CrewAI GLM演示测试脚本
用于快速测试系统功能
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """
    测试环境配置
    """
    print("🧪 测试环境配置...")
    
    # 测试环境变量
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key or api_key == "your_zhipuai_api_key_here":
        print("❌ ZHIPUAI_API_KEY未正确配置")
        return False
    
    print("✅ 环境变量配置正确")
    return True

def test_llm_connection():
    """
    测试GLM连接
    """
    print("🧪 测试GLM-4连接...")
    
    try:
        from llm_config import get_glm_llm
        llm = get_glm_llm()
        
        # 简单测试
        response = llm.invoke("请用一句话介绍人工智能。")
        print(f"✅ GLM-4响应: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ GLM-4连接失败: {str(e)}")
        return False

def test_agents():
    """
    测试代理创建
    """
    print("🧪 测试代理创建...")
    
    try:
        from agents import create_researcher_agent, create_reporting_analyst_agent
        
        # 创建代理
        researcher = create_researcher_agent()
        analyst = create_reporting_analyst_agent()
        
        print(f"✅ 研究员代理: {researcher.role}")
        print(f"✅ 分析师代理: {analyst.role}")
        return True
        
    except Exception as e:
        print(f"❌ 代理创建失败: {str(e)}")
        return False

def test_tasks():
    """
    测试任务创建
    """
    print("🧪 测试任务创建...")
    
    try:
        from tasks import create_all_tasks
        
        # 创建任务
        research_task, reporting_task = create_all_tasks("人工智能")
        
        print(f"✅ 研究任务: {research_task.description[:50]}...")
        print(f"✅ 报告任务: {reporting_task.description[:50]}...")
        return True
        
    except Exception as e:
        print(f"❌ 任务创建失败: {str(e)}")
        return False

def test_crew():
    """
    测试团队创建
    """
    print("🧪 测试团队创建...")
    
    try:
        from crew import create_research_crew
        
        # 创建团队
        crew = create_research_crew("人工智能", verbose=False)
        
        print(f"✅ 团队创建成功，主题: {crew.topic}")
        print(f"✅ 代理数量: {len(crew.crew.agents)}")
        print(f"✅ 任务数量: {len(crew.crew.tasks)}")
        return True
        
    except Exception as e:
        print(f"❌ 团队创建失败: {str(e)}")
        return False

def run_quick_test():
    """
    运行快速测试
    """
    print("🧪 运行快速研究测试...")
    
    try:
        from crew import run_research
        
        # 运行简单的研究
        print("开始研究'ChatGPT'...")
        result = run_research("ChatGPT", verbose=False)
        
        print("✅ 快速测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 快速测试失败: {str(e)}")
        return False

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("🧪 CrewAI GLM演示系统测试")
    print("=" * 60)
    
    tests = [
        ("环境配置", test_environment),
        ("GLM连接", test_llm_connection),
        ("代理创建", test_agents),
        ("任务创建", test_tasks),
        ("团队创建", test_crew),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        
        # 询问是否运行完整测试
        run_full = input("\n是否运行完整的研究测试? (y/N): ").strip().lower()
        if run_full in ['y', 'yes', '是']:
            run_quick_test()
    else:
        print("⚠️  部分测试失败，请检查配置。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
